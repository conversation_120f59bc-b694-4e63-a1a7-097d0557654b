#pragma once
#include <windows.h>

// SIMPLIFIED SECURITY CONFIGURATION
// Clean and simple security settings

namespace Security {
    
    // Basic security toggles
    struct Config {
        // Development mode - disables most security checks
        static bool IsDevelopmentMode() {
            #ifdef _DEBUG
                return true;
            #elif defined(DEV)
                return true;
            #else
                return false;
            #endif
        }
        
        // Security check enablers
        static bool EnableDebuggerDetection() { return !IsDevelopmentMode(); }
        static bool EnableVMDetection() { return !IsDevelopmentMode(); }
        static bool EnableIntegrityChecks() { return !IsDevelopmentMode(); }
        static bool EnableTimingChecks() { return !IsDevelopmentMode(); }
        
        // Popup control
        static bool ShowSecurityPopups() { return false; }  // Clean - no popups
        static bool ShowErrorPopups() { return true; }      // Keep error popups
        static bool ShowDebugInfo() { return IsDevelopmentMode(); }
    };
}

// Simple popup macros
#define SECURITY_MESSAGE(msg, title) \
    do { \
        if (Security::Config::ShowSecurityPopups()) { \
            MessageBoxA(NULL, msg, title, MB_OK | MB_ICONWARNING); \
        } \
    } while(0)

#define ERROR_MESSAGE(msg, title) \
    do { \
        if (Security::Config::ShowErrorPopups()) { \
            MessageBoxA(NULL, msg, title, MB_OK | MB_ICONERROR); \
        } \
    } while(0)

#define DEBUG_MESSAGE(msg, title) \
    do { \
        if (Security::Config::ShowDebugInfo()) { \
            MessageBoxA(NULL, msg, title, MB_OK | MB_ICONINFORMATION); \
        } \
    } while(0)

// Additional popup macros for compatibility
#define INFO_POPUP(hwnd, msg, title, flags) \
    do { \
        if (Security::Config::ShowErrorPopups()) { \
            MessageBoxA(hwnd, msg, title, flags); \
        } \
    } while(0)

#define ERROR_POPUP(hwnd, msg, title, flags) \
    do { \
        if (Security::Config::ShowErrorPopups()) { \
            MessageBoxA(hwnd, msg, title, flags); \
        } \
    } while(0)

#define SECURITY_POPUP(hwnd, msg, title, flags) \
    do { \
        if (Security::Config::ShowSecurityPopups()) { \
            MessageBoxA(hwnd, msg, title, flags); \
        } \
    } while(0)