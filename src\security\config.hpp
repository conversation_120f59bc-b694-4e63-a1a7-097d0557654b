#pragma once
#include <windows.h>  // For MessageBoxA
#include <tlhelp32.h>  // For process enumeration
#include <string>      // For std::string
#include <algorithm>   // For std::transform
#include <cstdint>     // For uint32_t
#include <cstdlib>     // For rand()
#include "../auth/skStr.h"  // For skCrypt

// ADVANCED SECURITY CONFIGURATION
// Professional anti-cracking and reverse engineering protection

// DYNAMIC CONFIGURATION SYSTEM
// Security settings are now calculated at runtime based on system characteristics
// This makes it much harder for crackers to simply patch compile-time flags

// Base security multiplier (higher = more secure, but may affect performance)
#define SECURITY_INTENSITY_LEVEL 3

// Feature toggles (these are now processed dynamically)
#define ENABLE_ADVANCED_PROTECTION 1
#define ENABLE_STEALTH_OPERATIONS 1
#define ENABLE_DETAILED_DIAGNOSTICS 1

// PORTABLE BUILD SIGNATURE (ersetzt hardcoded Werte)
// Wird zur Laufzeit durch portable Entropie ersetzt
#define BUILD_SIGNATURE_LEGACY 0xF5A1DE2C  // Fallback für Kompatibilität
#define USE_PORTABLE_KEYS 1  // Aktiviert portable Schlüssel-System