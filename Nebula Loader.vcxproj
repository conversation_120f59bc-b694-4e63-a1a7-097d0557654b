<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DEV|Win32">
      <Configuration>DEV</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DEV|x64">
      <Configuration>DEV</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{82212CF7-C4EA-436C-ADD5-F7AD56122BF5}</ProjectGuid>
    <RootNamespace>Nebula Loader</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>Nebula Loader</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DEV|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DEV|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DEV|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DEV|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(ProjectDir)Build\Debug\</OutDir>
    <IntDir>$(ProjectDir)Build\Debug\obj\</IntDir>
    <IncludePath>$(ProjectDir)external\imgui;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(ProjectDir)Build\Release\</OutDir>
    <IntDir>$(ProjectDir)Build\Release\obj\</IntDir>
    <IncludePath>$(ProjectDir)external\imgui;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DEV|Win32'">
    <OutDir>$(ProjectDir)Build\DEV\</OutDir>
    <IntDir>$(ProjectDir)Build\DEV\obj\</IntDir>
    <IncludePath>$(ProjectDir)external\imgui;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(ProjectDir)Build\Debug\</OutDir>
    <IntDir>$(ProjectDir)Build\Debug\obj\</IntDir>
    <IncludePath>$(ProjectDir)external\imgui;$(IncludePath)</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(ProjectDir)Build\Release\</OutDir>
    <IntDir>$(ProjectDir)Build\Release\obj\</IntDir>
    <IncludePath>$(ProjectDir)external\imgui;$(IncludePath)</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DEV|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(ProjectDir)Build\DEV\</OutDir>
    <IntDir>$(ProjectDir)Build\DEV\obj\</IntDir>
    <IncludePath>$(ProjectDir)external\imgui;$(ProjectDir)external\include;$(IncludePath)</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
    </Link>
    <PreBuildEvent>
      <Command>cd /d "$(ProjectDir)" &amp;&amp; powershell -ExecutionPolicy Bypass -File "update_signature.ps1"</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
      <PreprocessorDefinitions />
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <AdditionalDependencies>external\lib\library_x64.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PreBuildEvent>
      <Command>cd /d "$(ProjectDir)" &amp;&amp; powershell -ExecutionPolicy Bypass -File "update_signature.ps1"</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <PreBuildEvent>
      <Command>cd /d "$(ProjectDir)" &amp;&amp; powershell -ExecutionPolicy Bypass -File "update_signature.ps1"</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DEV|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <PreBuildEvent>
      <Command>cd /d "$(ProjectDir)" &amp;&amp; powershell -ExecutionPolicy Bypass -File "update_signature.ps1"</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>external\lib\library_x64.lib;iphlpapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PreBuildEvent>
      <Command>cd /d "$(ProjectDir)" &amp;&amp; powershell -ExecutionPolicy Bypass -File "update_signature.ps1"</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DEV|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
      <PreprocessorDefinitions>DEV;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalOptions>/FS /MP /Gm- /GL %(AdditionalOptions)</AdditionalOptions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>false</MinimalRebuild>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>external\lib\auth.lib;external\lib\freetype.lib;iphlpapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <LinkTimeCodeGeneration>UseLinkTimeCodeGeneration</LinkTimeCodeGeneration>
      <AdditionalOptions>/NODEFAULTLIB:LIBCMT /OPT:ICF /OPT:REF /INCREMENTAL:NO %(AdditionalOptions)</AdditionalOptions>
    </Link>
    <PreBuildEvent>
      <Command>cd /d "$(ProjectDir)" &amp;&amp; powershell -ExecutionPolicy Bypass -File "update_signature.ps1"</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="external\imgui\misc\cpp\imgui_stdlib.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="external\imgui\misc\freetype\imgui_freetype.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="src\pch.cpp">
      <PrecompiledHeader>Create</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="src\security\debug.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="src\security\config.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="src\security\security.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="src\security\core.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="src\security\indirect_crash.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="src\ui\ui.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_demo.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_draw.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_impl_dx9.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_impl_win32.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_tables.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_widgets.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClCompile Include="src\Main.cpp">
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="external\imgui\misc\cpp\imgui_stdlib.h" />
    <ClInclude Include="external\imgui\misc\freetype\imgui_freetype.h" />
    <ClInclude Include="external\imgui\misc\single_file\imgui_single_file.h" />
    <ClInclude Include="external\include\freetype\config\ftconfig.h" />
    <ClInclude Include="external\include\freetype\config\ftheader.h" />
    <ClInclude Include="external\include\freetype\config\ftmodule.h" />
    <ClInclude Include="external\include\freetype\config\ftoption.h" />
    <ClInclude Include="external\include\freetype\config\ftstdlib.h" />
    <ClInclude Include="external\include\freetype\config\integer-types.h" />
    <ClInclude Include="external\include\freetype\config\mac-support.h" />
    <ClInclude Include="external\include\freetype\config\public-macros.h" />
    <ClInclude Include="external\include\freetype\freetype.h" />
    <ClInclude Include="external\include\freetype\ftadvanc.h" />
    <ClInclude Include="external\include\freetype\ftbbox.h" />
    <ClInclude Include="external\include\freetype\ftbdf.h" />
    <ClInclude Include="external\include\freetype\ftbitmap.h" />
    <ClInclude Include="external\include\freetype\ftbzip2.h" />
    <ClInclude Include="external\include\freetype\ftcache.h" />
    <ClInclude Include="external\include\freetype\ftchapters.h" />
    <ClInclude Include="external\include\freetype\ftcid.h" />
    <ClInclude Include="external\include\freetype\ftcolor.h" />
    <ClInclude Include="external\include\freetype\ftdriver.h" />
    <ClInclude Include="external\include\freetype\fterrdef.h" />
    <ClInclude Include="external\include\freetype\fterrors.h" />
    <ClInclude Include="external\include\freetype\ftfntfmt.h" />
    <ClInclude Include="external\include\freetype\ftgasp.h" />
    <ClInclude Include="external\include\freetype\ftglyph.h" />
    <ClInclude Include="external\include\freetype\ftgxval.h" />
    <ClInclude Include="external\include\freetype\ftgzip.h" />
    <ClInclude Include="external\include\freetype\ftimage.h" />
    <ClInclude Include="external\include\freetype\ftincrem.h" />
    <ClInclude Include="external\include\freetype\ftlcdfil.h" />
    <ClInclude Include="external\include\freetype\ftlist.h" />
    <ClInclude Include="external\include\freetype\ftlogging.h" />
    <ClInclude Include="external\include\freetype\ftlzw.h" />
    <ClInclude Include="external\include\freetype\ftmac.h" />
    <ClInclude Include="external\include\freetype\ftmm.h" />
    <ClInclude Include="external\include\freetype\ftmodapi.h" />
    <ClInclude Include="external\include\freetype\ftmoderr.h" />
    <ClInclude Include="external\include\freetype\ftotval.h" />
    <ClInclude Include="external\include\freetype\ftoutln.h" />
    <ClInclude Include="external\include\freetype\ftparams.h" />
    <ClInclude Include="external\include\freetype\ftpfr.h" />
    <ClInclude Include="external\include\freetype\ftrender.h" />
    <ClInclude Include="external\include\freetype\ftsizes.h" />
    <ClInclude Include="external\include\freetype\ftsnames.h" />
    <ClInclude Include="external\include\freetype\ftstroke.h" />
    <ClInclude Include="external\include\freetype\ftsynth.h" />
    <ClInclude Include="external\include\freetype\ftsystem.h" />
    <ClInclude Include="external\include\freetype\fttrigon.h" />
    <ClInclude Include="external\include\freetype\fttypes.h" />
    <ClInclude Include="external\include\freetype\ftwinfnt.h" />
    <ClInclude Include="external\include\freetype\internal\autohint.h" />
    <ClInclude Include="external\include\freetype\internal\cffotypes.h" />
    <ClInclude Include="external\include\freetype\internal\cfftypes.h" />
    <ClInclude Include="external\include\freetype\internal\compiler-macros.h" />
    <ClInclude Include="external\include\freetype\internal\ftcalc.h" />
    <ClInclude Include="external\include\freetype\internal\ftdebug.h" />
    <ClInclude Include="external\include\freetype\internal\ftdrv.h" />
    <ClInclude Include="external\include\freetype\internal\ftgloadr.h" />
    <ClInclude Include="external\include\freetype\internal\fthash.h" />
    <ClInclude Include="external\include\freetype\internal\ftmemory.h" />
    <ClInclude Include="external\include\freetype\internal\ftmmtypes.h" />
    <ClInclude Include="external\include\freetype\internal\ftobjs.h" />
    <ClInclude Include="external\include\freetype\internal\ftpsprop.h" />
    <ClInclude Include="external\include\freetype\internal\ftrfork.h" />
    <ClInclude Include="external\include\freetype\internal\ftserv.h" />
    <ClInclude Include="external\include\freetype\internal\ftstream.h" />
    <ClInclude Include="external\include\freetype\internal\fttrace.h" />
    <ClInclude Include="external\include\freetype\internal\ftvalid.h" />
    <ClInclude Include="external\include\freetype\internal\psaux.h" />
    <ClInclude Include="external\include\freetype\internal\pshints.h" />
    <ClInclude Include="external\include\freetype\internal\services\svbdf.h" />
    <ClInclude Include="external\include\freetype\internal\services\svcfftl.h" />
    <ClInclude Include="external\include\freetype\internal\services\svcid.h" />
    <ClInclude Include="external\include\freetype\internal\services\svfntfmt.h" />
    <ClInclude Include="external\include\freetype\internal\services\svgldict.h" />
    <ClInclude Include="external\include\freetype\internal\services\svgxval.h" />
    <ClInclude Include="external\include\freetype\internal\services\svkern.h" />
    <ClInclude Include="external\include\freetype\internal\services\svmetric.h" />
    <ClInclude Include="external\include\freetype\internal\services\svmm.h" />
    <ClInclude Include="external\include\freetype\internal\services\svotval.h" />
    <ClInclude Include="external\include\freetype\internal\services\svpfr.h" />
    <ClInclude Include="external\include\freetype\internal\services\svpostnm.h" />
    <ClInclude Include="external\include\freetype\internal\services\svprop.h" />
    <ClInclude Include="external\include\freetype\internal\services\svpscmap.h" />
    <ClInclude Include="external\include\freetype\internal\services\svpsinfo.h" />
    <ClInclude Include="external\include\freetype\internal\services\svsfnt.h" />
    <ClInclude Include="external\include\freetype\internal\services\svttcmap.h" />
    <ClInclude Include="external\include\freetype\internal\services\svtteng.h" />
    <ClInclude Include="external\include\freetype\internal\services\svttglyf.h" />
    <ClInclude Include="external\include\freetype\internal\services\svwinfnt.h" />
    <ClInclude Include="external\include\freetype\internal\sfnt.h" />
    <ClInclude Include="external\include\freetype\internal\svginterface.h" />
    <ClInclude Include="external\include\freetype\internal\t1types.h" />
    <ClInclude Include="external\include\freetype\internal\tttypes.h" />
    <ClInclude Include="external\include\freetype\internal\wofftypes.h" />
    <ClInclude Include="external\include\freetype\otsvg.h" />
    <ClInclude Include="external\include\freetype\t1tables.h" />
    <ClInclude Include="external\include\freetype\ttnameid.h" />
    <ClInclude Include="external\include\freetype\tttables.h" />
    <ClInclude Include="external\include\freetype\tttags.h" />
    <ClInclude Include="external\include\ft2build.h" />
    <ClInclude Include="src\auth\auth.hpp" />
    <ClInclude Include="src\auth\json.hpp" />
    <ClInclude Include="src\auth\secure_auth.hpp" />
    <ClInclude Include="src\auth\skStr.h" />
    <ClInclude Include="src\auth\utils.hpp" />
    <ClInclude Include="src\font\font.hpp" />
    <ClInclude Include="src\globals.hpp" />
    <ClInclude Include="src\pch.h" />
    <ClInclude Include="src\security\obfuscation.hpp" />
    <ClInclude Include="src\security\protection.hpp" />
    <ClInclude Include="src\security\debug.hpp" />
    <ClInclude Include="src\security\config.hpp" />
    <ClInclude Include="src\security\encryption.hpp" />
    <ClInclude Include="src\security\integrity.hpp" />
    <ClInclude Include="src\security\security.hpp" />
    <ClInclude Include="src\security\settings.h" />
    <ClInclude Include="src\security\core.hpp" />
    <ClInclude Include="src\security\indirect_crash.hpp" />
    <ClInclude Include="src\security\macros.hpp" />
    <ClInclude Include="src\security\strings.hpp" />
    <ClInclude Include="src\ui\ui.hpp" />
    <ClInclude Include="external\imgui\imconfig.h" />
    <ClInclude Include="external\imgui\imgui.h" />
    <ClInclude Include="external\imgui\imgui_impl_dx9.h" />
    <ClInclude Include="external\imgui\imgui_impl_win32.h" />
    <ClInclude Include="external\imgui\imgui_internal.h" />
    <ClInclude Include="external\imgui\imstb_rectpack.h" />
    <ClInclude Include="external\imgui\imstb_textedit.h" />
    <ClInclude Include="external\imgui\imstb_truetype.h" />
    <ClInclude Include="src\Main.hpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>