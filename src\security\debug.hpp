#pragma once
#include <Windows.h>
#include <iostream>
#include <thread>
#include <vector>
#include <chrono>
#include <TlHelp32.h>
#include <Psapi.h>
#include <winternl.h>
#include <intrin.h>
#include <iphlpapi.h>
#include <random>
#include <algorithm>
#include "config.hpp"
#include "indirect_crash.hpp"
#include "../auth/skStr.h"  // Add skCrypt support

// ANTI-DEBUG PROTECTION SYSTEM
// Professional-grade protection against reverse engineering and debugging

namespace AntiDebugProtection {

    // Advanced Anti-Debug System
    class AntiDebug {
    private:
        static bool s_debugger_detected;
        static DWORD s_last_check_time;

    private:
        static const char* s_detection_reason;

    public:
        // Public setter for detection reason (for cross-class access)
        static void SetDetectionReason(const char* reason) {
            s_detection_reason = reason;
        }

        // Public getter for detection reason
        static const char* GetDetectionReason() {
            return s_detection_reason;
        }
        // Stealth function to hide from hooks
        static __forceinline bool StealthCheck() {
            volatile bool result = false;
            __try {
                // Multiple detection methods combined - NO HEAP FLAGS
                result = (IsDebuggerPresent() || CheckRemoteDebugger() ||
                         CheckPEBFlags() || CheckProcessDebugPort());
            }
            __except(EXCEPTION_EXECUTE_HANDLER) {
                result = true; // Exception during check = likely debugging
            }
            return result;
        }

    public:
        // Basic debugger detection with anti-hook protection - ENHANCED
        static bool IsDebuggerAttached() {

            // Method 1: Direct PEB check (most reliable)
            __try {
                PPEB peb = (PPEB)__readgsqword(0x60);
                if (peb && peb->BeingDebugged) {
                    s_detection_reason = skCrypt("Debugger directly attached to process (PEB.BeingDebugged = 1)").decrypt();
                    return true;
                }
            }
            __except(EXCEPTION_EXECUTE_HANDLER) {
                s_detection_reason = skCrypt("Exception during PEB access (likely debugging)").decrypt();
                return true;
            }

            // Method 2: Windows API check
            if (IsDebuggerPresent()) {
                s_detection_reason = skCrypt("Windows API reports debugger present").decrypt();
                return true;
            }

            // Method 3: Check for debug port
            HANDLE hProcess = GetCurrentProcess();
            DWORD debugPort = 0;
            typedef NTSTATUS(WINAPI* pNtQueryInformationProcess)(HANDLE, UINT, PVOID, ULONG, PULONG);
            pNtQueryInformationProcess NtQIP = (pNtQueryInformationProcess)GetProcAddress(
                GetModuleHandleA(skCrypt("ntdll.dll").decrypt()), skCrypt("NtQueryInformationProcess").decrypt());

            if (NtQIP && NtQIP(hProcess, 7, &debugPort, sizeof(debugPort), NULL) == 0 && debugPort != 0) {
                s_detection_reason = skCrypt("Debug port detected (NtQueryInformationProcess)").decrypt();
                return true;
            }

            return false;
        }

        // Remote debugger detection with multiple methods
        static bool CheckRemoteDebugger() {

            BOOL isRemote = FALSE;
            HANDLE hProcess = GetCurrentProcess();

            // Method 1: CheckRemoteDebuggerPresent
            if (CheckRemoteDebuggerPresent(hProcess, &isRemote) && isRemote) {
                return true;
            }

            // Method 2: NtQueryInformationProcess
            typedef NTSTATUS(WINAPI* pNtQueryInformationProcess)(HANDLE, UINT, PVOID, ULONG, PULONG);
            pNtQueryInformationProcess NtQIP = (pNtQueryInformationProcess)GetProcAddress(
                GetModuleHandleA(skCrypt("ntdll.dll").decrypt()), skCrypt("NtQueryInformationProcess").decrypt());

            if (NtQIP) {
                DWORD debugPort = 0;
                if (NtQIP(hProcess, 7, &debugPort, sizeof(debugPort), NULL) == 0) {
                    return (debugPort != 0);
                }
            }

            return false;
        }

        // Advanced PEB analysis with detailed detection
        static bool CheckPEBFlags() {

            __try {
                #ifdef _WIN64
                    BYTE* peb = (BYTE*)__readgsqword(0x60);
                #else
                    BYTE* peb = (BYTE*)__readfsdword(0x30);
                #endif

                if (peb) {
                    // BeingDebugged flag
                    if (peb[2] != 0) {
                        s_detection_reason = skCrypt("Active debugger attached to process").decrypt();
                        return true;
                    }

                    // Skip all NtGlobalFlag/heap checks - too many false positives
                }
            }
            __except(EXCEPTION_EXECUTE_HANDLER) {
                s_detection_reason = skCrypt("Memory access violation (likely debugging)").decrypt();
                return true;
            }
            return false;
        }

        // NT Global Flag detection - DISABLED (too many false positives)
        static bool CheckNTGlobalFlag() {
            return false; // Completely disabled
        }

        // Heap flags analysis - DISABLED (too many false positives)
        static bool CheckHeapFlags() {
            return false; // Completely disabled
        }

        // Process debug port check
        static bool CheckProcessDebugPort() {

            typedef NTSTATUS(WINAPI* pNtQueryInformationProcess)(HANDLE, UINT, PVOID, ULONG, PULONG);
            pNtQueryInformationProcess NtQIP = (pNtQueryInformationProcess)GetProcAddress(
                GetModuleHandleA(skCrypt("ntdll.dll").decrypt()), skCrypt("NtQueryInformationProcess").decrypt());

            if (NtQIP) {
                DWORD debugPort = 0;
                NTSTATUS status = NtQIP(GetCurrentProcess(), 7, &debugPort, sizeof(debugPort), NULL);
                return (status == 0 && debugPort != 0);
            }
            return false;
        }
        
        // Hardware breakpoint detection
        static bool CheckHardwareBreakpoints() {

            CONTEXT ctx = { 0 };
            ctx.ContextFlags = CONTEXT_DEBUG_REGISTERS;

            if (GetThreadContext(GetCurrentThread(), &ctx)) {
                return (ctx.Dr0 || ctx.Dr1 || ctx.Dr2 || ctx.Dr3);
            }
            return false;
        }

        // Timing-based detection
        static bool CheckTimingAttack() {

            LARGE_INTEGER start, end, freq;
            QueryPerformanceFrequency(&freq);
            QueryPerformanceCounter(&start);

            // Dummy operations
            volatile int dummy = 0;
            for (int i = 0; i < 1000; i++) {
                dummy += i * 2;
            }

            QueryPerformanceCounter(&end);
            double elapsed = (double)(end.QuadPart - start.QuadPart) / freq.QuadPart;

            // If execution took too long, likely being debugged
            return (elapsed > 0.01); // 10ms threshold
        }

        // Process enumeration for debugging tools - ENHANCED
        static bool CheckDebuggingProcesses();

        // Combined advanced detection
        static bool DetectDebugger();
        
        // Get specific solution based on detection method
        static const char* GetSpecificSolution(const char* method);

        // Stealth logging function
        static void LogDetection(const char* method);

        // Advanced reaction to detection
        static void HandleDetection(const char* method = nullptr);

        // Anti-tampering check
        static bool VerifyCodeIntegrity();
    };

    // VM Detection System - ENHANCED
    class VMDetection {
    private:
        static bool s_vm_detected;
        static std::string s_vm_type;

    public:
        // Main VM detection function
        static bool DetectVM();

        // CPUID-based detection
        static bool CheckCPUID();

        // Registry-based detection
        static bool CheckRegistry();

        // MAC address analysis
        static bool CheckMACAddress();

        // Process-based detection
        static bool CheckVMProcesses();

        // Hardware-based detection
        static bool CheckHardware();

        // Get detected VM type
        static const std::string& GetVMType() { return s_vm_type; }
    };

    // Integrity Protection System
    class IntegrityChecker {
    private:
        static std::vector<uint32_t> s_checksums;
        static bool s_initialized;

    public:
        // Initialize checksums
        static void Initialize();

        // Verify binary integrity
        static bool VerifyBinary();

        // Calculate CRC32
        static uint32_t CalculateCRC32(const void* data, size_t length);

        // Check for modifications
        static bool CheckModifications();
    };

    // Main Protection Controller
    class ProtectionController {
    private:
        static bool s_initialized;
        static bool s_monitoring_active;
        static std::thread s_monitor_thread;
        static std::atomic<bool> s_should_exit;

        // Background monitoring function
        static void MonitoringLoop();

    public:
        // Initialize protection system
        static void Initialize();

        // Shutdown protection system
        static void Shutdown();

        // Perform immediate check
        static bool PerformImmediateCheck();

        // Quick check (minimal overhead)
        static bool QuickCheck();

        // Start background monitoring
        static void StartMonitoring();

        // Stop background monitoring
        static void StopMonitoring();

        // Check if protection is active
        static bool IsActive() { return s_monitoring_active; }
    };
}

// Static variable definitions moved to anti_debug.cpp to avoid multiple definitions

// Anti-Debug Protection Macros
#define ANTI_DEBUG_INIT() AntiDebugProtection::ProtectionController::Initialize()
#define ANTI_DEBUG_CHECK() AntiDebugProtection::ProtectionController::PerformImmediateCheck()
#define ANTI_DEBUG_SHUTDOWN() AntiDebugProtection::ProtectionController::Shutdown()
#define ANTI_DEBUG_QUICK() AntiDebugProtection::ProtectionController::QuickCheck()

// Inline protection with multiple detection methods
#define INLINE_PROTECTION_CHECK() \
    do { \
        if (AntiDebugProtection::AntiDebug::IsDebuggerAttached() || \
            AntiDebugProtection::AntiDebug::CheckPEBFlags() || \
            AntiDebugProtection::AntiDebug::CheckHardwareBreakpoints()) { \
            AntiDebugProtection::AntiDebug::HandleDetection(skCrypt("Inline check").decrypt()); \
        } \
    } while(0)

// Stealth inline check (minimal footprint) - x64 compatible
#define STEALTH_CHECK() \
    do { \
        PPEB peb = (PPEB)__readgsqword(0x60); \
        if (peb && peb->BeingDebugged) { \
            IndirectCrash::SilentTermination::SilentExit(); \
        } \
    } while(0)

// VM detection check
#define VM_DETECTION_CHECK() \
    do { \
        if (AntiDebugProtection::VMDetection::DetectVM()) { \
            AntiDebugProtection::AntiDebug::HandleDetection(skCrypt("VM detected").decrypt()); \
        } \
    } while(0)

// Legacy compatibility macros
#define ENHANCED_PROTECTION_INIT() ANTI_DEBUG_INIT()
#define ENHANCED_PROTECTION_CHECK() ANTI_DEBUG_CHECK()
#define ENHANCED_PROTECTION_SHUTDOWN() ANTI_DEBUG_SHUTDOWN()
#define ADVANCED_PROTECTION_INIT() ANTI_DEBUG_INIT()
#define ADVANCED_PROTECTION_CHECK() ANTI_DEBUG_CHECK()
#define ADVANCED_PROTECTION_SHUTDOWN() ANTI_DEBUG_SHUTDOWN()
#define ADVANCED_PROTECTION_QUICK() ANTI_DEBUG_QUICK()
