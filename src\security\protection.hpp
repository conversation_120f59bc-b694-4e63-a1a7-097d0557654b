#pragma once
#include <windows.h>
#include <winternl.h>  // For PEB definition
#include <cstdint>
#include <string>
#include <tlhelp32.h>
#include <intrin.h>
#include "config.hpp"
#include "../auth/skStr.h"

// ADVANCED PROTECTION SYSTEM
// Provides anti-debugging and VM detection capabilities

namespace AdvancedProtection {
    
    namespace AntiDebug {
        // Detection reason storage
        static thread_local const char* s_detection_reason = nullptr;
        
        inline const char* GetDetectionReason() {
            return s_detection_reason ? s_detection_reason : skCrypt("Unknown").decrypt();
        }
        
        inline void SetDetectionReason(const char* reason) {
            s_detection_reason = reason;
        }
        
        inline bool IsDebuggerAttached() {
            if (SecurityConfig::DISABLE_DEBUGGER_CHECKS()) {
                return false;
            }
            
            // Check 1: IsDebuggerPresent API
            if (IsDebuggerPresent()) {
                SetDetectionReason(skCrypt("IsDebuggerPresent() returned true").decrypt());
                return true;
            }
            
            // Check 2: PEB BeingDebugged flag (simplified)
            __try {
                PPEB peb = (PPEB)__readgsqword(0x60);
                if (peb && peb->BeingDebugged) {
                    SetDetectionReason(skCrypt("PEB BeingDebugged flag is set").decrypt());
                    return true;
                }
            }
            __except(EXCEPTION_EXECUTE_HANDLER) {
                SetDetectionReason(skCrypt("Exception during PEB access - possible debugging").decrypt());
                return true;
            }
            
            return false;
        }
        
        inline bool CheckPEBFlags() {
            if (SecurityConfig::DISABLE_DEBUGGER_CHECKS()) {
                return false;
            }
            
            __try {
                PPEB peb = (PPEB)__readgsqword(0x60);
                if (peb) {
                    // Check BeingDebugged flag
                    if (peb->BeingDebugged) {
                        SetDetectionReason(skCrypt("PEB BeingDebugged flag detected").decrypt());
                        return true;
                    }
                    
                    // Note: NtGlobalFlag is not directly accessible in the public PEB structure
                    // This check is disabled for compatibility
                }
            }
            __except(EXCEPTION_EXECUTE_HANDLER) {
                SetDetectionReason(skCrypt("Exception during PEB flags check").decrypt());
                return true;
            }
            
            return false;
        }
        
        inline bool CheckHardwareBreakpoints() {
            if (SecurityConfig::DISABLE_DEBUGGER_CHECKS()) {
                return false;
            }
            
            CONTEXT ctx = {0};
            ctx.ContextFlags = CONTEXT_DEBUG_REGISTERS;
            
            if (GetThreadContext(GetCurrentThread(), &ctx)) {
                if (ctx.Dr0 || ctx.Dr1 || ctx.Dr2 || ctx.Dr3) {
                    SetDetectionReason(skCrypt("Hardware breakpoints detected in debug registers").decrypt());
                    return true;
                }
            }
            
            return false;
        }
        
        inline bool CheckDebuggingProcesses() {
            // In DEV mode: Allow development tools but block cracking tools
            const char* always_blocked[] = {
                skCrypt("x64dbg.exe").decrypt(), skCrypt("x32dbg.exe").decrypt(), skCrypt("ollydbg.exe").decrypt(),
                skCrypt("ida.exe").decrypt(), skCrypt("ida64.exe").decrypt(), skCrypt("idaq.exe").decrypt(), skCrypt("idaq64.exe").decrypt(),
                skCrypt("immunitydebugger.exe").decrypt(), skCrypt("cheatengine-x86_64.exe").decrypt()
            };

            // Check processes to scan
            const char** processes_to_check = always_blocked;
            size_t process_count = sizeof(always_blocked) / sizeof(always_blocked[0]);

            // In release mode, also block development tools
            if (!SecurityConfig::DISABLE_PROCESS_CHECKS()) {
                static const char* all_processes[] = {
                    skCrypt("x64dbg.exe").decrypt(), skCrypt("x32dbg.exe").decrypt(), skCrypt("ollydbg.exe").decrypt(), skCrypt("windbg.exe").decrypt(),
                    skCrypt("ida.exe").decrypt(), skCrypt("ida64.exe").decrypt(), skCrypt("idaq.exe").decrypt(), skCrypt("idaq64.exe").decrypt(),
                    skCrypt("immunitydebugger.exe").decrypt(), skCrypt("cheatengine-x86_64.exe").decrypt(),
                    skCrypt("processhacker.exe").decrypt(), skCrypt("procmon.exe").decrypt(), skCrypt("procexp.exe").decrypt()
                };
                processes_to_check = all_processes;
                process_count = sizeof(all_processes) / sizeof(all_processes[0]);
            }
            
            HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
            if (hSnapshot == INVALID_HANDLE_VALUE) {
                return false;
            }
            
            PROCESSENTRY32 pe32;
            pe32.dwSize = sizeof(PROCESSENTRY32);
            
            if (Process32First(hSnapshot, &pe32)) {
                do {
                    // Check against the selected process list
                    for (size_t i = 0; i < process_count; i++) {
                        if (_stricmp(pe32.szExeFile, processes_to_check[i]) == 0) {
                            CloseHandle(hSnapshot);
                            SetDetectionReason(skCrypt("Debugging process detected").decrypt());
                            return true;
                        }
                    }
                } while (Process32Next(hSnapshot, &pe32));
            }
            
            CloseHandle(hSnapshot);
            return false;
        }
    }
    
    namespace VMDetection {
        inline bool DetectVM() {
            if (SecurityConfig::DISABLE_VM_DETECTION()) {
                return false;
            }
            
            // Check 1: Registry VM indicators
            HKEY hKey;
            if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, skCrypt("SYSTEM\\CurrentControlSet\\Services\\VBoxService").decrypt(), 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
                RegCloseKey(hKey);
                return true;
            }
            
            // Check 2: VMware registry
            if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, skCrypt("SOFTWARE\\VMware, Inc.\\VMware Tools").decrypt(), 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
                RegCloseKey(hKey);
                return true;
            }
            
            // Check 3: Common VM processes
            const char* vm_processes[] = {
                skCrypt("vmtoolsd.exe").decrypt(), skCrypt("vmwaretray.exe").decrypt(), skCrypt("vmwareuser.exe").decrypt(),
                skCrypt("vboxservice.exe").decrypt(), skCrypt("vboxtray.exe").decrypt(), skCrypt("virtualbox.exe").decrypt()
            };
            
            HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
            if (hSnapshot != INVALID_HANDLE_VALUE) {
                PROCESSENTRY32 pe32;
                pe32.dwSize = sizeof(PROCESSENTRY32);
                
                if (Process32First(hSnapshot, &pe32)) {
                    do {
                        // Use C-style string comparison (case-insensitive)
                        if (_stricmp(pe32.szExeFile, skCrypt("vmtoolsd.exe").decrypt()) == 0 ||
                            _stricmp(pe32.szExeFile, skCrypt("vmwaretray.exe").decrypt()) == 0 ||
                            _stricmp(pe32.szExeFile, skCrypt("vmwareuser.exe").decrypt()) == 0 ||
                            _stricmp(pe32.szExeFile, skCrypt("vboxservice.exe").decrypt()) == 0 ||
                            _stricmp(pe32.szExeFile, skCrypt("vboxtray.exe").decrypt()) == 0 ||
                            _stricmp(pe32.szExeFile, skCrypt("virtualbox.exe").decrypt()) == 0) {
                            CloseHandle(hSnapshot);
                            return true;
                        }
                    } while (Process32Next(hSnapshot, &pe32));
                }
                CloseHandle(hSnapshot);
            }
            
            return false;
        }
    }
}