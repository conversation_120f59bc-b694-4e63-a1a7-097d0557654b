#pragma once
#include <windows.h>
#include <cstdint>
#include <psapi.h>  // For GetModuleInformation
#include "config.hpp"
#include "security.hpp"
#include "../auth/skStr.h"

// ADVANCED OBFUSCATION SYSTEM
// Provides polymorphic security and anti-patch protection

// Portable build signature function
inline uint32_t GetPortableBuildSignature() {
    // Generate a portable signature based on system characteristics
    static uint32_t cached_signature = 0;
    if (cached_signature == 0) {
        cached_signature = BUILD_SIGNATURE_LEGACY ^ GetTickCount() ^ GetCurrentProcessId();
    }
    return cached_signature;
}

namespace AdvancedObfuscation {
    
    namespace PolymorphicSecurity {
        // Polymorphic security state management
        static volatile uint32_t s_morph_state = 0x12345678;
        static volatile bool s_initialized = false;
        
        inline void InitializeMorphState() {
            if (!s_initialized) {
                s_morph_state = GetTickCount() ^ GetCurrentProcessId();
#if USE_PORTABLE_KEYS
                s_morph_state ^= GetPortableBuildSignature();
#else
                s_morph_state ^= BUILD_SIGNATURE_LEGACY;
#endif
                s_initialized = true;
            }
        }
        
        inline uint32_t GetMorphState() {
            if (!s_initialized) {
                InitializeMorphState();
            }
            return s_morph_state;
        }
        
        inline void UpdateMorphState() {
            s_morph_state = (s_morph_state << 7) ^ (s_morph_state >> 25);
            s_morph_state ^= GetTickCount();
        }
    }
    
    namespace AntiPatchProtection {
        // Function checksum storage
        static volatile uint32_t s_function_checksums[16] = {0};
        static volatile bool s_checksums_initialized = false;
        
        inline void InitializeFunctionChecksums() {
            if (!s_checksums_initialized) {
                // Initialize checksums with dummy values based on portable signature
                for (int i = 0; i < 16; i++) {
#if USE_PORTABLE_KEYS
                    s_function_checksums[i] = GetPortableBuildSignature() ^ (i * 0x9E3779B9);
#else
                    s_function_checksums[i] = BUILD_SIGNATURE_LEGACY ^ (i * 0x9E3779B9);
#endif
                }
                s_checksums_initialized = true;
            }
        }
        
        inline bool VerifyFunctionIntegrity(void* function_ptr) {
            if (!s_checksums_initialized) {
                InitializeFunctionChecksums();
            }
            
            // Simplified integrity check
            if (!function_ptr) return false;
            
            volatile uint8_t* ptr = (volatile uint8_t*)function_ptr;
            volatile uint32_t checksum = 0;
            
            // Calculate simple checksum of first few bytes
            for (int i = 0; i < 8; i++) {
                checksum ^= ptr[i] * (i + 1);
            }
            
            // Check against expected patterns (simplified)
            return (checksum != 0xDEADBEEF); // Avoid obvious patch signatures
        }
    }
}

// Obfuscation namespace extensions
namespace Obfuscation {
    
    namespace IntegrityChecker {
        static volatile bool s_integrity_initialized = false;
        static volatile uint32_t s_binary_checksum = 0;
        
        inline void InitializeChecksum() {
            if (!s_integrity_initialized) {
                // Calculate a simple checksum based on module handle
                HMODULE hModule = GetModuleHandle(NULL);
                if (hModule) {
                    // Use module handle as base for checksum
#if USE_PORTABLE_KEYS
                    s_binary_checksum = (uint32_t)(uintptr_t)hModule ^ GetPortableBuildSignature();
                } else {
                    s_binary_checksum = GetPortableBuildSignature() ^ 0xFEEDFACE;
#else
                    s_binary_checksum = (uint32_t)(uintptr_t)hModule ^ BUILD_SIGNATURE_LEGACY;
                } else {
                    s_binary_checksum = BUILD_SIGNATURE_LEGACY ^ 0xFEEDFACE;
#endif
                }
                s_integrity_initialized = true;
            }
        }
        
        inline bool VerifyIntegrity() {
            if (!s_integrity_initialized) {
                InitializeChecksum();
            }
            return s_binary_checksum != 0;
        }
    }
    
    namespace StackProtection {
        static volatile uint32_t s_stack_canary = 0;
        static volatile bool s_canary_initialized = false;
        
        inline void InitializeCanary() {
            if (!s_canary_initialized) {
#if USE_PORTABLE_KEYS
                s_stack_canary = GetTickCount() ^ GetCurrentProcessId() ^ GetPortableBuildSignature();
#else
                s_stack_canary = GetTickCount() ^ GetCurrentProcessId() ^ BUILD_SIGNATURE_LEGACY;
#endif
                s_canary_initialized = true;
            }
        }
        
        inline bool VerifyCanary() {
            return s_canary_initialized && (s_stack_canary != 0);
        }
        
        inline uint32_t GetCanary() {
            if (!s_canary_initialized) {
                InitializeCanary();
            }
            return s_stack_canary;
        }
    }

    namespace MemoryProtection {
        inline void ClearSensitiveData(void* data, size_t size) {
            if (data && size > 0) {
                // Securely clear memory by overwriting with random data
                volatile char* ptr = (volatile char*)data;
                for (size_t i = 0; i < size; i++) {
                    ptr[i] = (char)(GetTickCount() ^ i);
                }
                // Second pass with zeros
                for (size_t i = 0; i < size; i++) {
                    ptr[i] = 0;
                }
            }
        }
    }
}