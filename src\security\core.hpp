#pragma once
// SECURITY CORE SYSTEM
// Advanced obfuscation and runtime protection
// Common headers now in pch.h
#include <tlhelp32.h>
#include <psapi.h>
#include "config.hpp"
#include "indirect_crash.hpp"
#include "../auth/skStr.h"

#pragma comment(lib, "iphlpapi.lib")
#pragma comment(lib, "psapi.lib")
#pragma comment(lib, "ws2_32.lib")

namespace SecurityCore {

    // Runtime configuration calculator - replaces obvious compile-time flags
    class RuntimeConfig {
    private:
        static uint32_t s_config_seed;
        static bool s_initialized;
        
        // Calculate configuration based on system characteristics
        static uint32_t CalculateConfigSeed() {
            uint32_t seed = 0;
            
            // Use system info to generate seed
            SYSTEM_INFO si;
            GetSystemInfo(&si);
            seed ^= si.dwNumberOfProcessors;
            seed ^= si.dwPageSize;
            seed ^= (uint32_t)(uintptr_t)si.lpMinimumApplicationAddress;
            
            // Add timing component
            LARGE_INTEGER freq, counter;
            QueryPerformanceFrequency(&freq);
            QueryPerformanceCounter(&counter);
            seed ^= (uint32_t)(counter.QuadPart % 0xFFFFFFFF);
            seed ^= (uint32_t)(freq.QuadPart % 0xFFFFFFFF);
            
            // Add process info
            seed ^= GetCurrentProcessId();
            seed ^= GetCurrentThreadId();
            
            return seed;
        }
        
    public:
        static void Initialize() {
            if (!s_initialized) {
                s_config_seed = CalculateConfigSeed();
                s_initialized = true;
            }
        }
        
        // Dynamic security level calculation - harder to patch than simple flags
        static bool IsSecurityEnabled(uint32_t check_id) {
            Initialize();
            
            // Complex calculation that's hard to predict
            uint32_t hash = s_config_seed;
            hash ^= check_id;
            hash = (hash << 13) | (hash >> 19); // Rotate
            hash ^= 0x9E3779B9; // Golden ratio constant
            hash *= 0x85EBCA6B; // Multiply by prime
            
            // Different checks have different thresholds
            uint32_t threshold = 0x80000000; // 50% chance base
            
            // Adjust threshold based on check type
            switch (check_id % 8) {
                case 0: threshold = 0x60000000; break; // VM detection - 37.5%
                case 1: threshold = 0x90000000; break; // Debug detection - 56.25%
                case 2: threshold = 0x70000000; break; // Integrity - 43.75%
                case 3: threshold = 0x85000000; break; // Hook detection - 52%
                case 4: threshold = 0x75000000; break; // Memory protection - 46%
                case 5: threshold = 0x95000000; break; // Hardware BP - 58%
                case 6: threshold = 0x65000000; break; // Thread monitoring - 40%
                case 7: threshold = 0x88000000; break; // API monitoring - 53%
            }
            
            return hash > threshold;
        }
        
        // Get obfuscated boolean for development mode
        static bool IsDevelopmentEnvironment() {
            Initialize();

            // Check for development indicators
            bool devMode = false;

            // REMOVED: IsDebuggerPresent() check - this was disabling all security!
            // We want to DETECT and BLOCK debuggers, not disable security when found!

            // Simplified development environment check
            // Check for common development environment variables
            if (GetEnvironmentVariableA(skCrypt("VSAPPIDDIR").decrypt(), NULL, 0) > 0 ||
                GetEnvironmentVariableA(skCrypt("VS160COMNTOOLS").decrypt(), NULL, 0) > 0 ||
                GetEnvironmentVariableA(skCrypt("VSCODE_PID").decrypt(), NULL, 0) > 0) {
                devMode = true;
            }

            return devMode;
        }
    };

    // Sophisticated junk code that looks functional
    class AdvancedJunkCode {
    private:
        static std::vector<uint32_t> s_fake_data;
        static uint32_t s_fake_counter;
        
    public:
        // Fake cryptographic operations
        static uint32_t FakeCrypto(uint32_t input) {
            // Looks like real crypto but is just obfuscation
            uint32_t result = input;
            result ^= 0x5A827999; // SHA-1 constant
            result = (result << 7) | (result >> 25);
            result ^= 0x6ED9EBA1; // SHA-1 constant
            result = (result << 11) | (result >> 21);
            result ^= 0x8F1BBCDC; // SHA-1 constant
            result = (result << 13) | (result >> 19);
            result ^= 0xCA62C1D6; // SHA-1 constant
            
            // Hidden security check within fake crypto
            if (RuntimeConfig::IsSecurityEnabled(0x1001)) {
                if (IsDebuggerPresent()) {
                    ANTI_DEBUGGER_CRASH();
                }
            }
            
            return result;
        }
        
        // Fake network operations
        static bool FakeNetworkCheck() {
            // Simulate network activity
            WSADATA wsaData;
            if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
                return false;
            }
            
            // Create fake socket operations
            SOCKET sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
            if (sock != INVALID_SOCKET) {
                // Hidden VM check within network code
                if (RuntimeConfig::IsSecurityEnabled(0x1002)) {
                    int cpuInfo[4];
                    __cpuid(cpuInfo, 1);
                    if (cpuInfo[2] & (1 << 31)) { // Hypervisor bit
                        closesocket(sock);
                        WSACleanup();
                        INDIRECT_CRASH_METHOD(2); // Stack overflow crash
                    }
                }
                
                closesocket(sock);
            }
            
            WSACleanup();
            return true;
        }
        
        // Fake file operations with hidden integrity check
        static bool FakeFileOperations() {
            // Simulate file operations
            char tempPath[MAX_PATH];
            GetTempPathA(MAX_PATH, tempPath);
            
            std::string fakePath = std::string(tempPath) + skCrypt("fake_temp.tmp").decrypt();
            HANDLE hFile = CreateFileA(fakePath.c_str(), GENERIC_WRITE, 0, NULL, 
                                     CREATE_ALWAYS, FILE_ATTRIBUTE_TEMPORARY | FILE_FLAG_DELETE_ON_CLOSE, NULL);
            
            if (hFile != INVALID_HANDLE_VALUE) {
                // Write fake data
                DWORD written;
                const char* fakeData = skCrypt("FAKE_DATA_FOR_OBFUSCATION").decrypt();
                WriteFile(hFile, fakeData, static_cast<DWORD>(strlen(fakeData)), &written, NULL);
                
                // Hidden integrity check
                if (RuntimeConfig::IsSecurityEnabled(0x1003)) {
                    HMODULE hMod = GetModuleHandle(NULL);
                    if (hMod) {
                        PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)hMod;
                        if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) {
                            CloseHandle(hFile);
                            INDIRECT_CRASH_METHOD(3); // Invalid memory access
                        }
                    }
                }
                
                CloseHandle(hFile);
                return true;
            }
            
            return false;
        }
        
        // Fake registry operations with hidden hook detection
        static bool FakeRegistryCheck() {
            HKEY hKey;
            LONG result = RegOpenKeyExA(HKEY_LOCAL_MACHINE,
                                       skCrypt("SOFTWARE\\Microsoft\\Windows\\CurrentVersion").decrypt(),
                                       0, KEY_READ, &hKey);
            
            if (result == ERROR_SUCCESS) {
                char buffer[256];
                DWORD bufferSize = sizeof(buffer);
                
                // Read some registry value
                RegQueryValueExA(hKey, skCrypt("ProgramFilesDir").decrypt(), NULL, NULL,
                               (LPBYTE)buffer, &bufferSize);
                
                // Hidden hook detection within registry code
                if (RuntimeConfig::IsSecurityEnabled(0x1004)) {
                    HMODULE hKernel32 = GetModuleHandleA(skCrypt("kernel32.dll").decrypt());
                    if (hKernel32) {
                        FARPROC pIsDebuggerPresent = GetProcAddress(hKernel32, skCrypt("IsDebuggerPresent").decrypt());
                        if (pIsDebuggerPresent) {
                            // Check if function is hooked by examining first bytes
                            BYTE* funcBytes = (BYTE*)pIsDebuggerPresent;
                            if (funcBytes[0] == 0xE9 || funcBytes[0] == 0xEB) { // JMP instructions
                                RegCloseKey(hKey);
                                INDIRECT_CRASH_METHOD(4); // Heap corruption
                            }
                        }
                    }
                }
                
                RegCloseKey(hKey);
                return true;
            }
            
            return false;
        }
    };

    // Polymorphic security checks that change behavior
    class PolymorphicSecurity {
    private:
        static uint32_t s_morph_state;

    public:
        static void InitializeMorphState() {
            s_morph_state = GetTickCount() ^ GetCurrentProcessId();
        }

        // Security check that changes its method based on runtime state
        static bool MorphingSecurityCheck() {
            if (s_morph_state == 0) InitializeMorphState();

            uint32_t method = s_morph_state % 4;
            s_morph_state = (s_morph_state * 1103515245 + 12345) & 0x7FFFFFFF; // LCG

            switch (method) {
                case 0: {
                    // Method 1: PEB check
                    PPEB peb = (PPEB)__readgsqword(0x60);
                    return peb && peb->BeingDebugged;
                }
                case 1: {
                    // Method 2: Hardware breakpoint check
                    CONTEXT ctx = { 0 };
                    ctx.ContextFlags = CONTEXT_DEBUG_REGISTERS;
                    if (GetThreadContext(GetCurrentThread(), &ctx)) {
                        return (ctx.Dr0 || ctx.Dr1 || ctx.Dr2 || ctx.Dr3);
                    }
                    return false;
                }
                case 2: {
                    // Method 3: Timing check
                    LARGE_INTEGER start, end, freq;
                    QueryPerformanceFrequency(&freq);
                    QueryPerformanceCounter(&start);

                    Sleep(1);

                    QueryPerformanceCounter(&end);
                    double elapsed = (double)(end.QuadPart - start.QuadPart) / freq.QuadPart;
                    return elapsed > 0.1; // If sleep took too long, might be debugged
                }
                case 3: {
                    // Method 4: Exception-based check
                    __try {
                        __debugbreak();
                        return true; // If we reach here, debugger handled the exception
                    }
                    __except (EXCEPTION_EXECUTE_HANDLER) {
                        return false; // Normal execution
                    }
                }
            }

            return false;
        }
    };

    // Anti-patch protection with checksums
    class AntiPatchProtection {
    private:
        static std::vector<uint32_t> s_function_checksums;
        static bool s_checksums_initialized;

        // Calculate checksum of a function
        static uint32_t CalculateFunctionChecksum(void* func_ptr, size_t size) {
            uint32_t checksum = 0;
            uint8_t* bytes = (uint8_t*)func_ptr;

            for (size_t i = 0; i < size; i++) {
                checksum = (checksum << 1) ^ bytes[i];
                checksum ^= (i + 1) * 0x9E3779B9; // Add position-dependent salt
            }

            return checksum;
        }

    public:
        // Initialize checksums for critical functions
        static void InitializeFunctionChecksums() {
            if (s_checksums_initialized) return;

            s_function_checksums.clear();

            // Add checksums for critical security functions
            // Note: In real implementation, these would be calculated at build time
            HMODULE hKernel32 = GetModuleHandleA(skCrypt("kernel32.dll").decrypt());
            if (hKernel32) {
                FARPROC pIsDebuggerPresent = GetProcAddress(hKernel32, skCrypt("IsDebuggerPresent").decrypt());
                if (pIsDebuggerPresent) {
                    uint32_t checksum = CalculateFunctionChecksum(pIsDebuggerPresent, 32);
                    s_function_checksums.push_back(checksum);
                }
            }

            s_checksums_initialized = true;
        }

        // Verify that critical functions haven't been patched
        static bool VerifyFunctionIntegrity() {
            if (!s_checksums_initialized) {
                InitializeFunctionChecksums();
                return true; // First run, assume OK
            }

            // Check IsDebuggerPresent function
            HMODULE hKernel32 = GetModuleHandleA(skCrypt("kernel32.dll").decrypt());
            if (hKernel32 && !s_function_checksums.empty()) {
                FARPROC pIsDebuggerPresent = GetProcAddress(hKernel32, skCrypt("IsDebuggerPresent").decrypt());
                if (pIsDebuggerPresent) {
                    uint32_t current_checksum = CalculateFunctionChecksum(pIsDebuggerPresent, 32);
                    if (current_checksum != s_function_checksums[0]) {
                        return false; // Function has been modified
                    }
                }
            }

            return true;
        }
    };

    // Static member declarations (definitions moved to .cpp file)
    // These are now properly defined in security_core.cpp
}

// Advanced Junk Code Macros - Look functional but hide security checks
#define JUNK_CRYPTO_OP() \
    do { \
        volatile uint32_t temp = GetTickCount(); \
        temp = SecurityCore::AdvancedJunkCode::FakeCrypto(temp); \
        temp ^= GetCurrentThreadId(); \
    } while(0)

#define JUNK_NETWORK_OP() \
    do { \
        if (GetTickCount() % 100 == 0) { \
            SecurityCore::AdvancedJunkCode::FakeNetworkCheck(); \
        } \
    } while(0)

#define JUNK_FILE_OP() \
    do { \
        if (GetCurrentProcessId() % 50 == 0) { \
            SecurityCore::AdvancedJunkCode::FakeFileOperations(); \
        } \
    } while(0)

#define JUNK_REGISTRY_OP() \
    do { \
        if (GetCurrentThreadId() % 75 == 0) { \
            SecurityCore::AdvancedJunkCode::FakeRegistryCheck(); \
        } \
    } while(0)

// Polymorphic security check hidden in junk code
#define MORPH_SECURITY_CHECK() \
    do { \
        if (SecurityCore::RuntimeConfig::IsSecurityEnabled(0x2001)) { \
            if (SecurityCore::PolymorphicSecurity::MorphingSecurityCheck()) { \
                INDIRECT_CRASH_METHOD(5); \
            } \
        } \
    } while(0)

// Anti-patch verification hidden in junk code
#define VERIFY_INTEGRITY() \
    do { \
        if (SecurityCore::RuntimeConfig::IsSecurityEnabled(0x2002)) { \
            if (!SecurityCore::AntiPatchProtection::VerifyFunctionIntegrity()) { \
                INDIRECT_CRASH_METHOD(6); \
            } \
        } \
    } while(0)

// Complex junk code that combines multiple operations
#define COMPLEX_JUNK_1() \
    do { \
        JUNK_CRYPTO_OP(); \
        MORPH_SECURITY_CHECK(); \
        JUNK_NETWORK_OP(); \
    } while(0)

#define COMPLEX_JUNK_2() \
    do { \
        JUNK_FILE_OP(); \
        VERIFY_INTEGRITY(); \
        JUNK_REGISTRY_OP(); \
    } while(0)

#define COMPLEX_JUNK_3() \
    do { \
        JUNK_CRYPTO_OP(); \
        JUNK_FILE_OP(); \
        MORPH_SECURITY_CHECK(); \
    } while(0)

// Random junk code selector
#define RANDOM_JUNK() \
    do { \
        switch (GetTickCount() % 3) { \
            case 0: COMPLEX_JUNK_1(); break; \
            case 1: COMPLEX_JUNK_2(); break; \
            case 2: COMPLEX_JUNK_3(); break; \
        } \
    } while(0)
