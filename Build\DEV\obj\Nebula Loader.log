﻿  PORTABLE SECURITY BUILD SYSTEM
  ==============================
  Generating new BUILD_SIGNATURE_LEGACY: 0x5086D94C
  Note: Portable system uses runtime entropy, this is just for fallback
  Successfully updated BUILD_SIGNATURE_LEGACY to: 0x5086D94C
  
  Build signature update completed.
  Main.cpp
  config.cpp
  core.cpp
  debug.cpp
  security.cpp
  ui.cpp
LINK : fatal error LNK1104: cannot open file 'atls.lib'
