﻿  PORTABLE SECURITY BUILD SYSTEM
  ==============================
  Generating new BUILD_SIGNATURE_LEGACY: 0xBDE36E33
  Note: Portable system uses runtime entropy, this is just for fallback
  Successfully updated BUILD_SIGNATURE_LEGACY to: 0xBDE36E33
  
  Build signature update completed.
  Main.cpp
  config.cpp
  core.cpp
  debug.cpp
  security.cpp
  ui.cpp
LINK : fatal error LNK1104: cannot open file 'atls.lib'
