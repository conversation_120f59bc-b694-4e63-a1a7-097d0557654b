﻿  PORTABLE SECURITY BUILD SYSTEM
  ==============================
  Generating new BUILD_SIGNATURE_LEGACY: 0xF5A1DE2C
  Note: Portable system uses runtime entropy, this is just for fallback
  Successfully updated BUILD_SIGNATURE_LEGACY to: 0xF5A1DE2C
  
  Build signature update completed.
  pch.cpp
  imgui_stdlib.cpp
  imgui_freetype.cpp
  imgui.cpp
  imgui_demo.cpp
  imgui_draw.cpp
  imgui_impl_dx9.cpp
  imgui_impl_win32.cpp
  imgui_tables.cpp
  imgui_widgets.cpp
  debug.cpp
  config.cpp
  security.cpp
  core.cpp
  indirect_crash.cpp
  ui.cpp
  Main.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\config.cpp(6,19): error C2653: 'DynamicConfig': is not a class or namespace name
  (compiling source file '/src/security/config.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\config.cpp(7,10): error C2653: 'DynamicConfig': is not a class or namespace name
  (compiling source file '/src/security/config.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\debug.cpp(137,9): error C2653: 'SecurityConfig': is not a class or namespace name
  (compiling source file '/src/security/debug.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\debug.cpp(137,25): error C3861: 'SHOW_DETAILED_SECURITY_ERRORS': identifier not found
  (compiling source file '/src/security/debug.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\debug.cpp(151,9): error C2653: 'SecurityConfig': is not a class or namespace name
  (compiling source file '/src/security/debug.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\debug.cpp(151,25): error C3861: 'ENABLE_DEBUG_MODE': identifier not found
  (compiling source file '/src/security/debug.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\debug.cpp(153,13): error C2653: 'SecurityConfig': is not a class or namespace name
  (compiling source file '/src/security/debug.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\debug.cpp(153,29): error C3861: 'SECURITY_POPUPS_ENABLED': identifier not found
  (compiling source file '/src/security/debug.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\debug.cpp(187,9): error C2653: 'SecurityConfig': is not a class or namespace name
  (compiling source file '/src/security/debug.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\debug.cpp(187,25): error C3861: 'DISABLE_VM_DETECTION': identifier not found
  (compiling source file '/src/security/debug.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\protection.hpp(53,17): error C2653: 'SecurityConfig': is not a class or namespace name
  (compiling source file '/src/Main.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\protection.hpp(53,33): error C3861: 'DISABLE_DEBUGGER_CHECKS': identifier not found
  (compiling source file '/src/Main.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\protection.hpp(79,17): error C2653: 'SecurityConfig': is not a class or namespace name
  (compiling source file '/src/Main.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\protection.hpp(79,33): error C3861: 'DISABLE_DEBUGGER_CHECKS': identifier not found
  (compiling source file '/src/Main.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\protection.hpp(109,18): error C2653: 'SecurityConfig': is not a class or namespace name
  (compiling source file '/src/Main.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\protection.hpp(109,34): error C3861: 'DISABLE_PROCESS_CHECKS': identifier not found
  (compiling source file '/src/Main.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\protection.hpp(148,17): error C2653: 'SecurityConfig': is not a class or namespace name
  (compiling source file '/src/Main.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\security\protection.hpp(148,33): error C3861: 'DISABLE_VM_DETECTION': identifier not found
  (compiling source file '/src/Main.cpp')
  
