﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="external\imgui\imgui.cpp">
      <Filter>External\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_demo.cpp">
      <Filter>External\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_draw.cpp">
      <Filter>External\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_impl_dx9.cpp">
      <Filter>External\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_impl_win32.cpp">
      <Filter>External\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_widgets.cpp">
      <Filter>External\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_tables.cpp">
      <Filter>External\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="src\Main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ui\ui.cpp">
      <Filter>Source Files\UI</Filter>
    </ClCompile>
    <ClCompile Include="src\security\config.cpp" />
    <ClCompile Include="src\security\debug.cpp" />
    <ClCompile Include="src\security\security.cpp" />
    <ClCompile Include="src\security\core.cpp" />
    <ClCompile Include="src\security\indirect_crash.cpp" />
    <ClCompile Include="external\imgui\misc\cpp\imgui_stdlib.cpp" />
    <ClCompile Include="external\imgui\misc\freetype\imgui_freetype.cpp" />
    <ClCompile Include="src\pch.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="external\imgui\imconfig.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="external\imgui\imgui.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="external\imgui\imgui_impl_dx9.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="external\imgui\imgui_impl_win32.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="external\imgui\imgui_internal.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="external\imgui\imstb_rectpack.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="external\imgui\imstb_textedit.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="external\imgui\imstb_truetype.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="src\Main.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ui\ui.hpp">
      <Filter>Header Files\UI</Filter>
    </ClInclude>
    <ClInclude Include="src\globals.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\auth\auth.hpp">
      <Filter>Header Files\Auth</Filter>
    </ClInclude>
    <ClInclude Include="src\auth\skStr.h">
      <Filter>Header Files\Auth</Filter>
    </ClInclude>
    <ClInclude Include="src\auth\utils.hpp">
      <Filter>Header Files\Auth</Filter>
    </ClInclude>
    <ClInclude Include="src\auth\json.hpp">
      <Filter>Header Files\Auth</Filter>
    </ClInclude>
    <ClInclude Include="src\auth\secure_auth.hpp" />
    <ClInclude Include="src\security\config.hpp" />
    <ClInclude Include="src\security\obfuscation.hpp" />
    <ClInclude Include="src\security\protection.hpp" />
    <ClInclude Include="src\security\debug.hpp" />
    <ClInclude Include="src\security\encryption.hpp" />
    <ClInclude Include="src\security\integrity.hpp" />
    <ClInclude Include="src\security\security.hpp" />
    <ClInclude Include="src\security\settings.h" />
    <ClInclude Include="src\security\core.hpp" />
    <ClInclude Include="src\security\macros.hpp" />
    <ClInclude Include="src\security\strings.hpp" />
    <ClInclude Include="src\security\indirect_crash.hpp" />
    <ClInclude Include="external\imgui\misc\cpp\imgui_stdlib.h" />
    <ClInclude Include="external\imgui\misc\freetype\imgui_freetype.h" />
    <ClInclude Include="external\imgui\misc\single_file\imgui_single_file.h" />
    <ClInclude Include="external\include\freetype\config\ftconfig.h" />
    <ClInclude Include="external\include\freetype\config\ftheader.h" />
    <ClInclude Include="external\include\freetype\config\ftmodule.h" />
    <ClInclude Include="external\include\freetype\config\ftoption.h" />
    <ClInclude Include="external\include\freetype\config\ftstdlib.h" />
    <ClInclude Include="external\include\freetype\config\integer-types.h" />
    <ClInclude Include="external\include\freetype\config\mac-support.h" />
    <ClInclude Include="external\include\freetype\config\public-macros.h" />
    <ClInclude Include="external\include\freetype\freetype.h" />
    <ClInclude Include="external\include\freetype\ftadvanc.h" />
    <ClInclude Include="external\include\freetype\ftbbox.h" />
    <ClInclude Include="external\include\freetype\ftbdf.h" />
    <ClInclude Include="external\include\freetype\ftbitmap.h" />
    <ClInclude Include="external\include\freetype\ftbzip2.h" />
    <ClInclude Include="external\include\freetype\ftcache.h" />
    <ClInclude Include="external\include\freetype\ftchapters.h" />
    <ClInclude Include="external\include\freetype\ftcid.h" />
    <ClInclude Include="external\include\freetype\ftcolor.h" />
    <ClInclude Include="external\include\freetype\ftdriver.h" />
    <ClInclude Include="external\include\freetype\fterrdef.h" />
    <ClInclude Include="external\include\freetype\fterrors.h" />
    <ClInclude Include="external\include\freetype\ftfntfmt.h" />
    <ClInclude Include="external\include\freetype\ftgasp.h" />
    <ClInclude Include="external\include\freetype\ftglyph.h" />
    <ClInclude Include="external\include\freetype\ftgxval.h" />
    <ClInclude Include="external\include\freetype\ftgzip.h" />
    <ClInclude Include="external\include\freetype\ftimage.h" />
    <ClInclude Include="external\include\freetype\ftincrem.h" />
    <ClInclude Include="external\include\freetype\ftlcdfil.h" />
    <ClInclude Include="external\include\freetype\ftlist.h" />
    <ClInclude Include="external\include\freetype\ftlogging.h" />
    <ClInclude Include="external\include\freetype\ftlzw.h" />
    <ClInclude Include="external\include\freetype\ftmac.h" />
    <ClInclude Include="external\include\freetype\ftmm.h" />
    <ClInclude Include="external\include\freetype\ftmodapi.h" />
    <ClInclude Include="external\include\freetype\ftmoderr.h" />
    <ClInclude Include="external\include\freetype\ftotval.h" />
    <ClInclude Include="external\include\freetype\ftoutln.h" />
    <ClInclude Include="external\include\freetype\ftparams.h" />
    <ClInclude Include="external\include\freetype\ftpfr.h" />
    <ClInclude Include="external\include\freetype\ftrender.h" />
    <ClInclude Include="external\include\freetype\ftsizes.h" />
    <ClInclude Include="external\include\freetype\ftsnames.h" />
    <ClInclude Include="external\include\freetype\ftstroke.h" />
    <ClInclude Include="external\include\freetype\ftsynth.h" />
    <ClInclude Include="external\include\freetype\ftsystem.h" />
    <ClInclude Include="external\include\freetype\fttrigon.h" />
    <ClInclude Include="external\include\freetype\fttypes.h" />
    <ClInclude Include="external\include\freetype\ftwinfnt.h" />
    <ClInclude Include="external\include\freetype\internal\autohint.h" />
    <ClInclude Include="external\include\freetype\internal\cffotypes.h" />
    <ClInclude Include="external\include\freetype\internal\cfftypes.h" />
    <ClInclude Include="external\include\freetype\internal\compiler-macros.h" />
    <ClInclude Include="external\include\freetype\internal\ftcalc.h" />
    <ClInclude Include="external\include\freetype\internal\ftdebug.h" />
    <ClInclude Include="external\include\freetype\internal\ftdrv.h" />
    <ClInclude Include="external\include\freetype\internal\ftgloadr.h" />
    <ClInclude Include="external\include\freetype\internal\fthash.h" />
    <ClInclude Include="external\include\freetype\internal\ftmemory.h" />
    <ClInclude Include="external\include\freetype\internal\ftmmtypes.h" />
    <ClInclude Include="external\include\freetype\internal\ftobjs.h" />
    <ClInclude Include="external\include\freetype\internal\ftpsprop.h" />
    <ClInclude Include="external\include\freetype\internal\ftrfork.h" />
    <ClInclude Include="external\include\freetype\internal\ftserv.h" />
    <ClInclude Include="external\include\freetype\internal\ftstream.h" />
    <ClInclude Include="external\include\freetype\internal\fttrace.h" />
    <ClInclude Include="external\include\freetype\internal\ftvalid.h" />
    <ClInclude Include="external\include\freetype\internal\psaux.h" />
    <ClInclude Include="external\include\freetype\internal\pshints.h" />
    <ClInclude Include="external\include\freetype\internal\services\svbdf.h" />
    <ClInclude Include="external\include\freetype\internal\services\svcfftl.h" />
    <ClInclude Include="external\include\freetype\internal\services\svcid.h" />
    <ClInclude Include="external\include\freetype\internal\services\svfntfmt.h" />
    <ClInclude Include="external\include\freetype\internal\services\svgldict.h" />
    <ClInclude Include="external\include\freetype\internal\services\svgxval.h" />
    <ClInclude Include="external\include\freetype\internal\services\svkern.h" />
    <ClInclude Include="external\include\freetype\internal\services\svmetric.h" />
    <ClInclude Include="external\include\freetype\internal\services\svmm.h" />
    <ClInclude Include="external\include\freetype\internal\services\svotval.h" />
    <ClInclude Include="external\include\freetype\internal\services\svpfr.h" />
    <ClInclude Include="external\include\freetype\internal\services\svpostnm.h" />
    <ClInclude Include="external\include\freetype\internal\services\svprop.h" />
    <ClInclude Include="external\include\freetype\internal\services\svpscmap.h" />
    <ClInclude Include="external\include\freetype\internal\services\svpsinfo.h" />
    <ClInclude Include="external\include\freetype\internal\services\svsfnt.h" />
    <ClInclude Include="external\include\freetype\internal\services\svttcmap.h" />
    <ClInclude Include="external\include\freetype\internal\services\svtteng.h" />
    <ClInclude Include="external\include\freetype\internal\services\svttglyf.h" />
    <ClInclude Include="external\include\freetype\internal\services\svwinfnt.h" />
    <ClInclude Include="external\include\freetype\internal\sfnt.h" />
    <ClInclude Include="external\include\freetype\internal\svginterface.h" />
    <ClInclude Include="external\include\freetype\internal\t1types.h" />
    <ClInclude Include="external\include\freetype\internal\tttypes.h" />
    <ClInclude Include="external\include\freetype\internal\wofftypes.h" />
    <ClInclude Include="external\include\freetype\otsvg.h" />
    <ClInclude Include="external\include\freetype\t1tables.h" />
    <ClInclude Include="external\include\freetype\ttnameid.h" />
    <ClInclude Include="external\include\freetype\tttables.h" />
    <ClInclude Include="external\include\freetype\tttags.h" />
    <ClInclude Include="external\include\ft2build.h" />
    <ClInclude Include="src\font\font.hpp" />
    <ClInclude Include="src\pch.h" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="External">
      <UniqueIdentifier>{8cf77124-bebc-471d-acac-8b9b1ffc2a66}</UniqueIdentifier>
    </Filter>
    <Filter Include="External\ImGui">
      <UniqueIdentifier>{1a2b3c4d-5e6f-7890-abcd-ef1234567890}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Auth">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Security">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\UI">
      <UniqueIdentifier>{39987C36-1234-5678-9ABC-DEF012345678}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995381-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Auth">
      <UniqueIdentifier>{67DA6AB7-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Security">
      <UniqueIdentifier>{39987C37-1234-5678-9ABC-DEF012345678}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\UI">
      <UniqueIdentifier>{12345678-9ABC-DEF0-1234-56789ABCDEF0}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>